{% extends 'base.html' %}

{% block content %}
    <div class="container-fluid main-container">
        <div class="row">
            <div class="col content-area-container">
                <textarea class="border rounded stream-voice-content-area" id="voice-content" readonly></textarea>
                <textarea class="border rounded stream-voice-content-area2" id="voice-content2" readonly></textarea>
                <div class="stream-bottom-container">
                    <button id="stream-record-button" type="button" class="btn btn-primary float-right" data-type="asr">
                        开始录音
                        <span class="fi-microphone"></span>
                    </button>
                    <button hidden id="stream-recording-button" type="button" class="btn btn-primary float-right">
                        停止录音
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div align="center" style="font-size:16px;color:#000"> &nbsp;&nbsp;&nbsp;&nbsp;    联系电话: 0592-5998812 &nbsp;&nbsp;&nbsp;&nbsp;        
     <a href="https://www.talentedsoft.com/" style=" color:#000;">公司网站链接</a> &nbsp;&nbsp;        
{% endblock content %}

{% block scripts %}
    {{ super() }}
    <script type="text/javascript" src="{{ url_for('static', filename='js/websocket_recorder.js') }}"></script>
{% endblock scripts %}
