# -*- coding: utf-8 -*-
import os, requests, json, re, traceback, time, io
from requests import HTTPError

from flask import request, current_app, Blueprint, jsonify, url_for, send_file
from sqlalchemy import and_

from voice_presentation_sys.utils import general_requests_func
from voice_presentation_sys.extensions import db
from voice_presentation_sys.models import User


ajax_bp = Blueprint('ajax', __name__)


@ajax_bp.route('/tts', methods=['POST'])
def word_tts():
    """
    语音合成
    根据post请求内携带的按键id，选择不同语言类型和人声端口进行http请求。
    """
    url = current_app.config['WORD_TTS_URL']
    get_wav_url = current_app.config['WORD_TTS_GET_WAV_PATH_URL']
    spk_id = int(request.values.get('spk_id'))  # 说话人id
    lan_id = int(request.values.get('lan_id'))  # 发音类型id
    content = request.values.get('content')

    # 测试链接
    if lan_id == 1:
        url = "https://tc.talentedsoft.com:58124/58104/dotctts"
        get_wav_url = "https://tc.talentedsoft.com:58124/58104/tts/"
        lan_id = 3

    valid_id_list = [1, 2, 3, 4, 5, 6, 7]  # 合法的spk_id范围
    if spk_id not in valid_id_list:
        return jsonify(
            state="Error",
            msg=f"不合法的发音类别，{lan_id}"
        )

    params = {'userid': os.getenv('USERID'), 'token': os.getenv('TOKEN'), 'content': content,
              'spkid': spk_id, 'lanid': lan_id}

    try:
        res = requests.post(url=url, data=params)
    except Exception as e:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="tts服务请求失败"
        )

    try:
        # 这里获取wavfile  ==>  res.json()['wavfile']
        pattern_get_wavfile = r"\"wavfile\":\"(\d+/\d+/\d+_\d+.wav)\""
        w = re.search(pattern_get_wavfile, res.text)
        tmp1 = '{' + w.group() + '}'
        res_wavfile = json.loads(tmp1)['wavfile']
    except:
        res_wavfile = res.json()['wavfile']

    try:
        # 这里获取errCode  ==>  res.json()['errCode']
        pattern_get_errCode = r"\"errCode\":\"(\d+?)\""
        e = re.search(pattern_get_errCode, res.text)
        tmp2 = '{' + e.group() + '}'
        res_errCode = json.loads(tmp2)['errCode']
    except:
        res_errCode = res.json()['errCode']

    if res_errCode != '0':
        current_app.logger.warn(f"tts服务异常, errCode:{res_errCode}.")
        return jsonify(
            state="Error",
            msg="合成异常，errCode:{res_errCode}."
        )
    elif res_errCode == '0':
        return jsonify(
            state="Success",
            msg=get_wav_url + '/' + res_wavfile
        )


@ajax_bp.route('/asr', methods=['POST'])
def wav_asr():
    """
    语音识别
    :param sex_asr_sign: 标志位, 为True是为性别识别, 为False则为语音识别
    :param file_tyle: 标志位, upload_file是为此次为上传文件，record_file是为此次为实时录音文件
    """
    params = {'userid': os.getenv('USERID'), 'token': os.getenv('TOKEN')}
    if request.values.get('file_type') == 'upload_file':
        file = request.files['wav_file'].read()
    else:
        file = request.files['wav_blob'].read()

    sex_asr_sign = False
    if request.values.get('type') == 'sex-asr':
        url = current_app.config['WAV_SEX_ASR_URL']
        sex_asr_sign = True
    elif request.values.get('type') == 'asr':
        url = current_app.config['WAV_ASR_URL']
    else:
        url = current_app.config['MINNAN_WAV_ASR_URL']
        
    try:
        session = requests.Session()
        res = session.post(url=url, data=params, files={'file': file})
        res_json = json.loads(res.text, strict=False)
    except:
        current_app.logger.warn(traceback.format_exc())

    if sex_asr_sign:
        res_json_err = '0'  # 目前性别识别只返回sex=0或1
    else:
        res_json_err = res_json['errCode']

    if res_json_err != '0':  # 语音不行保存下来看看
        time_now = str(int(time.time()))
        save_path = os.path.join(current_app.config['ASR_INVALID_DIR'], time_now)
        with open(save_path + '.wav', mode='wb') as f:
            f.write(file)
        current_app.logger.warn(f"asr返回错误码有误 errCode:{res_json_err} path:{save_path}")
        return jsonify(
            state="Error",
            msg="Server error."
        )
    elif res_json_err == '0':
        if sex_asr_sign:
            sex = res_json['sex']
            if sex == 0:
                content = '男'
            elif sex == 1:
                content = '女'
            else:
                content = '服务器出错，请重试'
        else:
            content = res_json['result']
        return jsonify(
            state="Success",
            msg=content
        )


@ajax_bp.route('/voiceprint', methods=['POST'])
def voiceprint():
    """
    声纹页面的所有ajax操作的错误处理和正确返回。
    """

    # 获取当前配置
    operation_type = request.values.get('operation_type')
    if operation_type == 'text-independent':
        current_config = current_app.config['TEXT_INDEPENDENT']
    elif operation_type == 'text-related':
        current_config = current_app.config['TEXT_RELATED']
    else:  # operation_type == 'dynamic-password'
        current_config = current_app.config['DYNAMIC_PASSWORD']

    # 获取上传的语音文件并读取
    if request.values.get('file_type') == 'upload_file':
        file = request.files['wav_file'].read()  # wav_file是上传文件的默认名称
    else:  # 此时request.values.get('file_type') == 'record_file':
        file = request.files['wav_blob'].read()

    userid = request.values.get('userid')

    # 防止数据库异常
    try:
        # 查询userid是否存在文本或口令,否则为默认值。
        user = User.query.filter(and_(User.userid == userid, User.register_type == operation_type)).first()
    except Exception as e:
        return jsonify(
            msg="服务器异常, 请联系管理员.")
    try:
        register_word = user.register_word
        if register_word:
            content = register_word
        else:
            content = "123456"
    except:
        content = "123456"

    # 判断operation值是否为"null"，如是则为声纹登记操作(区别为文本无关，相关，口令)，否则为 声纹确认/声纹辨认的操作。
    operation = request.values.get('operation')
    if operation != "null":
        if operation == 'voiceprint-confirm':
            res_json = general_requests_func(url=current_config['CONFIRM_URL'], userid=userid,
                                             token=current_config['TOKEN'], content=content, file=file)
            if res_json['errCode'] == 0 and float(res_json['score']) >= 0.6: #yhy2024-05-28
                return jsonify(
                    msg="声纹确认成功，当前语音确认结果分数为: " + str(res_json['score'])
                )
            else:
                return jsonify(
                    msg="声纹确认失败，语音确认结果不合格。分数为: " + str(res_json['score'])
                )

        elif operation == 'voiceprint-recognition':
            res_json = general_requests_func(url=current_config['RECOGNITION_URL'], userid=userid,
                                             token=current_config['TOKEN'], content=content, file=file)
            if res_json['errCode'] == 0:
                pattern = r'\[.*\]'
                id_list = re.search(pattern, res_json['msg'])
                return jsonify(
                    msg="当前语音辨认结果最高分ID为: " + id_list.group()[1:-1].split(" ")[0]
                )
            else:
                return jsonify(
                    msg="声纹辨认失败，请重试。"
                )
        else:
            return jsonify(
                msg='当前页面出错，请刷新页面后重试。'
            )
    else:
        # 注册用户
        res_json = general_requests_func(url=current_config['REGISTER_URL'], userid=userid,
                                         token=current_config['TOKEN'])
        if res_json['errCode'] != 0:
            return jsonify(
                msg='用户注册失败，请更换userid再次重试。'
            )

        # 数据库录入用户，以便在前端的声纹确认/辨认，给出注册时的口令/文本
        if operation_type == "text-independent":
            user = User(userid=userid, register_type=operation_type)
        elif operation_type == "text-related":
            user = User(userid=userid, register_type=operation_type, register_word="智能软件")
        else:
            user = User(userid=userid, register_type=operation_type, register_word="67032589")
        db.session.add(user)
        db.session.commit()

        # 当前注册通过，然后添加语音+训练模型。
        res_json = requests.post(url=current_config['ADD_SAMPLE_URL'], data={'userid': userid,  # 添加语音
                                                                             'token': current_config['TOKEN'], 'step': 1, 'content': content}, files={'file': file}).json()

        if res_json['errCode'] == 0:
            res_json = general_requests_func(url=current_config['TRAIN_MODEL_URL'], userid=userid,  # 训练模型
                                             token=current_config['TOKEN'])

            if res_json['errCode'] == 0:
                return jsonify(
                    msg="用户注册成功，模型生成完毕。"
                )
            else:
                # 模型训练失败则删除用户(线上模型和数据库都要删除)，提示重新注册。
                requests.post(url=current_config['DEL_USER_URL'], data={'userid': userid, 'token': current_config['TOKEN']})
                user = User.query.filter(and_(User.userid == userid, User.register_type == operation_type)).first()
                db.session.delete(user)
                db.session.commit()

                return jsonify(
                    msg="训练模型失败，请重新注册。"
                )
        else:
            # 语音添加失败则删除用户(线上模型和数据库都要删除)，提示重新注册。
            requests.post(url=current_config['DEL_USER_URL'], data={'userid': userid, 'token': current_config['TOKEN']})
            user = User.query.filter(and_(User.userid == userid, User.register_type == operation_type)).first()
            db.session.delete(user)
            db.session.commit()

            return jsonify(
                msg="添加语音失败，请重新注册，若仍为相同错误，请联系管理员。"
            )


# 这个函数用来查询用户的口令or文本
@ajax_bp.route('/detect_user_register_word/<string:userid>/<string:register_type>', methods=["GET"])
def detect_user_register_word(userid, register_type):
    try:
        user = User.query.filter(and_(User.userid == userid, User.register_type == register_type)).first()
        return jsonify(
            state='Success',
            msg=user.register_word
        )
    except:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state='Error',
            msg='Server error.'
        )


# 这个函数用数据库userid来验证是否注册(未注册或存在多个用户则返回)
@ajax_bp.route('/detect_user_register/<string:userid>/<string:register_type>', methods=["GET"])
def detect_user_register(userid, register_type):
    try:
        user_list = User.query.filter(and_(User.userid == userid, User.register_type == register_type)).all()
        if len(user_list) >= 1:
            return jsonify(
                state="Error"
            )
        else:
            return jsonify(
                state="Success"
            )
    except:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state='Error',
            msg='Server error.'
        )


@ajax_bp.route('/tts2/dotctraintts', methods=['POST'])
def train_tts():
    subuser = request.values.get('subuser')
    token = os.getenv('TOKEN')
    userid = os.getenv('USERID')

    file_type = request.values.get('file_type')
    if file_type:
        if file_type == 'upload_file':
            file = request.files['wav_file'].read()
        else:  # record_file
            file = request.files['wav_blob'].read()
    else:
        return jsonify(
            state="Error",
            msg="file_type is invalid."
        )

    params = {'userid': userid, 'token': token, 'subuser': subuser}

    url = current_app.config['TRAIN_TTS_URL']
    try:
        res = requests.post(url=url, data=params, files={'file': file})
        res_json = json.loads(res.text, strict=False)
    except:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="服务异常"
        )

    res_json_err = res_json['errCode']

    if res_json_err != '0':  # 语音不行保存下来看看
        current_app.logger.warn(f"train_tts 返回错误码 {res_json}")
        return jsonify(
            state="Error",
            msg="训练异常"
        )
    elif res_json_err == '0':
        return jsonify(
            state="Success",
            msg=res_json['result']
        )


@ajax_bp.route('/tts2/dotcgentts', methods=['GET', 'POST'])
def gen_tts():
    subuser = request.values.get('subuser')
    content = request.values.get('content')
    userid = os.getenv('USERID')
    token = os.getenv('TOKEN')

    params = {'userid': userid, 'token': token, 'content': content, 'subuser': subuser}
    url = current_app.config['GEN_TTS_URL']
    try:
        res = requests.get(url=url, params=params)
        if res.status_code != 200:
            raise HTTPError
    except Exception as e:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="合成语音异常"
        )
    else:
        return send_file(
            io.BytesIO(res.content),
            mimetype="audio/wav",
            cache_timeout=0  # 不要缓存文件，否则同样的请求将复用之前的文件内容。
        )


@ajax_bp.route('/denoise', methods=['POST'])
def denoise():
    """
    语音降噪
    """
    params = {'userid': os.getenv('USERID'), 'token': os.getenv('TOKEN')}
    if request.values.get('file_type') == 'upload_file':
        file = request.files['wav_file'].read()
    else:
        file = request.files['wav_blob'].read()
    res = requests.post(url=current_app.config["DENOISE_URL"], data=params, files={'file': file})
    print(current_app.config["DENOISE_URL"])
    print("res: ", res.content)
    res_json = json.loads(res.text, strict=False)
    current_app.logger.warn(traceback.format_exc())

    res_json_err = res_json['errCode']

    if res_json_err != '0':  # 语音不行保存下来看看
        current_app.logger.warn(f"denoise返回错误码有误 errCode:{res_json_err}")
        return jsonify(
            state="Error",
            msg="Server error."
        )
    else:
        return jsonify(
            state="Success",
            msg=res_json["wavfile"]
        )


@ajax_bp.route('/denoise/wav', methods=['GET', 'POST'])
def denoise_get_wav():
    wavfile = request.values.get('wavfile')
    url = current_app.config["GET_DENOISE_WAV"] + '/' + wavfile
    print(url)
    try:
        res = requests.get(url=url)
        status_code = res.status_code
        if status_code != 200:
            if status_code == 404:
                return jsonify(
                    state="Error",
                    msg="语音暂未生成"
                ), 404
            else:
                raise HTTPError
        else:
            pass
    except Exception as e:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="语音处理异常"
        ), 404

    return send_file(
        io.BytesIO(res.content),
        mimetype="audio/wav",
        cache_timeout=0  # 不要缓存文件，否则同样的请求将复用之前的文件内容。
    ), 200
