import argparse
import torch
import numpy as np
from model import SSL_nes2net
from accelerate import Accelerator, load_checkpoint_in_model
import gradio as gr

def get_args_parser(add_help=True):
    parser = argparse.ArgumentParser(description="ASVspoof 5",
                                     add_help=add_help)
    parser.add_argument('--batch_size', type=int, default=128)
    parser.add_argument('--num_workers', type=int, default=8)
    parser.add_argument('--loss', type=str, default='CE+OC')
    parser.add_argument('--PTM', type=str, default='None')
    parser.add_argument('--Melspec', action='store_true', default=False)
    parser.add_argument('--n_mels', type=int, default=120)
    parser.add_argument('--seed',
                        type=int,
                        default=1234,
                        help='random seed (default: 1234)')
    parser.add_argument('--model_path',
                        type=str,
                        default=None,
                        help='Model checkpoint')

    parser.add_argument('--eval', action='store_true', default=False)
    parser.add_argument('--eval_online', action='store_true', default=False)

    return parser
def crop_three_parts(audio, part_length = 64600):
    crops = []
    if len(audio) < part_length:
        padding = part_length - len(audio)
        audio = np.pad(audio, (0, padding), mode='constant')
        crops = [audio,audio,audio]
    else:
        start_crop = audio[:part_length]
        crops.append(start_crop)
        # 中间4秒
        half_len = len(audio) // 2
        mid_start = half_len - (part_length // 2)
        mid_crop = audio[mid_start:mid_start + part_length]
        crops.append(mid_crop)

        end_crop = audio[-part_length:]
        crops.append(end_crop)

    return np.stack(crops, axis=0)
def model_predict(audio):
    if audio is None:
        return "请先上传音频文件或录制音频"
    
    try:
        # 使用全局变量
        global device, model
        
        if device.type == 'cuda':
            torch.cuda.synchronize()
            start_event = torch.cuda.Event(enable_timing=True)
            end_event = torch.cuda.Event(enable_timing=True)
            start_event.record()
        else:
            import time
            start_time = time.time()

        sr, X = audio
        
        if X is None or len(X) == 0:
            return "音频文件无效，请重新上传"
        
        mean = np.mean(X)
        std = np.std(X)
        if std > 0:
            X = (X - mean) / std
        X_pad = crop_three_parts(X)
        x_inp = torch.Tensor(X_pad)

        with torch.no_grad():
            probabilities = 0
            for i in range(x_inp.size(0)):
                batch_in = x_inp[i]
                batch_in = batch_in.to(torch.float32).to(device)
                feat_output, _ = model(batch_in.unsqueeze(0))
                probabilities = probabilities + torch.softmax(feat_output, dim=1)
            probabilities = probabilities / 3
        
        predicted_class = torch.argmax(probabilities, dim=1).item()
        if predicted_class == 0:
            predit = "否"
        else:
            predit = "是"
        
        if device.type == 'cuda':
            end_event.record()
            torch.cuda.synchronize()
            infer_time = start_event.elapsed_time(end_event)
        else:
            end_time = time.time()
            infer_time = (end_time - start_time) * 1000
        
        device_info = f"GPU: {torch.cuda.get_device_name(0)}" if device.type == 'cuda' else "CPU"
        result = f">>是否AI生成音频: {predit}\n>>置信度: {100*probabilities[:,predicted_class].item():.2f}%\n>>处理时间:{infer_time:.1f} ms\n>>运行设备: {device_info}"
        
        return result
        
    except Exception as e:
        return f"处理出错: {str(e)}"

if __name__ == '__main__':
    args = get_args_parser().parse_args()

    # 直接使用 torch.device，不用 accelerator
    if torch.cuda.is_available():
        device = torch.device('cuda:0')
        print(f'Using GPU: {torch.cuda.get_device_name(0)}')
    else:
        device = torch.device('cpu')
        print('Using CPU')
    
    print('Device: {}'.format(device))

    model = SSL_nes2net(device)

    # load model 
    load_checkpoint_in_model(model, args.model_path)
    print(f'Model loaded:{args.model_path}')

    # 确保模型在正确设备上
    model = model.to(device)
    
    #eval
    model.eval()
    
    # 将 device 设为全局变量，供 model_predict 使用
    globals()['device'] = device
    globals()['model'] = model
    input_audio = gr.Audio(sources=["upload","microphone"],
                            waveform_options=gr.WaveformOptions(
                                waveform_color="#0066B4",
                                waveform_progress_color="#4338CA",
                                skip_length=2,
                                show_controls=False,
                                trim_region_color="#DDD5E9",
                                sample_rate=16000),
                            label="待测音频",
                            interactive=True)
    demo = gr.Interface(fn=model_predict,
                        inputs=input_audio,
                        submit_btn="开始检测",
                        outputs=[gr.Textbox(label="鉴定结果", lines=3)],
                        clear_btn="删除音频",
                        title="厦门大学智能语音实验室--AIGC音频检测系统",
                        theme="soft",flagging_options=[('反馈错误','')])
    demo.launch(share=True)
    # demo.launch(share=True)





