<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="asr_sys" uuid="8ae4a278-c0b6-4030-beb5-a8fe808cb619">
      <driver-ref>sqlite.xerial</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
      <jdbc-url>jdbc:sqlite:C:\Users\<USER>\Desktop\flask_program\voice_presentation_sys_pad\voice_presentation_sys\asr_sys.db</jdbc-url>
    </data-source>
    <data-source source="LOCAL" name="data" uuid="a5c4f53e-6876-4f03-bfc8-ffa8e70d5c37">
      <driver-ref>sqlite.xerial</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
      <jdbc-url>jdbc:sqlite:C:\Users\<USER>\Desktop\flask_program\voice_presentation_sys_pad\voice_presentation_sys\data.db</jdbc-url>
    </data-source>
  </component>
</project>