<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="74">
            <item index="0" class="java.lang.String" itemvalue="mysqlclient" />
            <item index="1" class="java.lang.String" itemvalue="psycopg2" />
            <item index="2" class="java.lang.String" itemvalue="raven" />
            <item index="3" class="java.lang.String" itemvalue="djangorestframework-jwt" />
            <item index="4" class="java.lang.String" itemvalue="django-crispy-forms" />
            <item index="5" class="java.lang.String" itemvalue="django-import-export" />
            <item index="6" class="java.lang.String" itemvalue="httplib2" />
            <item index="7" class="java.lang.String" itemvalue="xlsxwriter" />
            <item index="8" class="java.lang.String" itemvalue="django-reversion" />
            <item index="9" class="java.lang.String" itemvalue="django" />
            <item index="10" class="java.lang.String" itemvalue="coreapi" />
            <item index="11" class="java.lang.String" itemvalue="drf-extensions" />
            <item index="12" class="java.lang.String" itemvalue="django-filter" />
            <item index="13" class="java.lang.String" itemvalue="future" />
            <item index="14" class="java.lang.String" itemvalue="django-redis" />
            <item index="15" class="java.lang.String" itemvalue="markdown" />
            <item index="16" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="17" class="java.lang.String" itemvalue="djangorestframework" />
            <item index="18" class="java.lang.String" itemvalue="django-cors-headers" />
            <item index="19" class="java.lang.String" itemvalue="django-formtools" />
            <item index="20" class="java.lang.String" itemvalue="greenlet" />
            <item index="21" class="java.lang.String" itemvalue="PyYAML" />
            <item index="22" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="23" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="24" class="java.lang.String" itemvalue="Click" />
            <item index="25" class="java.lang.String" itemvalue="contextlib2" />
            <item index="26" class="java.lang.String" itemvalue="Faker" />
            <item index="27" class="java.lang.String" itemvalue="mccabe" />
            <item index="28" class="java.lang.String" itemvalue="certifi" />
            <item index="29" class="java.lang.String" itemvalue="monotonic" />
            <item index="30" class="java.lang.String" itemvalue="entrypoints" />
            <item index="31" class="java.lang.String" itemvalue="Flask-Dropzone" />
            <item index="32" class="java.lang.String" itemvalue="dnspython" />
            <item index="33" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="34" class="java.lang.String" itemvalue="python-engineio" />
            <item index="35" class="java.lang.String" itemvalue="Flask-WTF" />
            <item index="36" class="java.lang.String" itemvalue="xpinyin" />
            <item index="37" class="java.lang.String" itemvalue="Flask-CKEditor" />
            <item index="38" class="java.lang.String" itemvalue="gunicorn" />
            <item index="39" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="40" class="java.lang.String" itemvalue="Flask-Moment" />
            <item index="41" class="java.lang.String" itemvalue="python-socketio" />
            <item index="42" class="java.lang.String" itemvalue="Flask-SocketIO" />
            <item index="43" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="44" class="java.lang.String" itemvalue="Mako" />
            <item index="45" class="java.lang.String" itemvalue="idna" />
            <item index="46" class="java.lang.String" itemvalue="alembic" />
            <item index="47" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="48" class="java.lang.String" itemvalue="async-contextlib" />
            <item index="49" class="java.lang.String" itemvalue="Flask-DebugToolbar" />
            <item index="50" class="java.lang.String" itemvalue="pycodestyle" />
            <item index="51" class="java.lang.String" itemvalue="requests" />
            <item index="52" class="java.lang.String" itemvalue="Jinja2" />
            <item index="53" class="java.lang.String" itemvalue="flake8" />
            <item index="54" class="java.lang.String" itemvalue="Flask-Login" />
            <item index="55" class="java.lang.String" itemvalue="eventlet" />
            <item index="56" class="java.lang.String" itemvalue="Flask-Avatars" />
            <item index="57" class="java.lang.String" itemvalue="urllib3" />
            <item index="58" class="java.lang.String" itemvalue="Flask" />
            <item index="59" class="java.lang.String" itemvalue="blinker" />
            <item index="60" class="java.lang.String" itemvalue="coverage" />
            <item index="61" class="java.lang.String" itemvalue="six" />
            <item index="62" class="java.lang.String" itemvalue="Flask-Cors" />
            <item index="63" class="java.lang.String" itemvalue="pyflakes" />
            <item index="64" class="java.lang.String" itemvalue="text-unidecode" />
            <item index="65" class="java.lang.String" itemvalue="chardet" />
            <item index="66" class="java.lang.String" itemvalue="WTForms" />
            <item index="67" class="java.lang.String" itemvalue="pathtools" />
            <item index="68" class="java.lang.String" itemvalue="watchdog" />
            <item index="69" class="java.lang.String" itemvalue="Flask-Mail" />
            <item index="70" class="java.lang.String" itemvalue="Flask-Migrate" />
            <item index="71" class="java.lang.String" itemvalue="Bootstrap-Flask" />
            <item index="72" class="java.lang.String" itemvalue="argh" />
            <item index="73" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>