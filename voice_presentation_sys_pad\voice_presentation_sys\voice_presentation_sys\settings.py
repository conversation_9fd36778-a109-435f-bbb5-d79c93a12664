# -*- coding: utf-8 -*-

import os
import sys

basedir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

# SQLite URI compatible
WIN = sys.platform.startswith('win')
if WIN:
    prefix = 'sqlite:///'
else:
    prefix = 'sqlite:////'


class BaseConfig(object):

    DENOISE_URL = "http://125.77.202.194:58124/Denoise"

    GET_DENOISE_WAV = "http://125.77.202.194:58124/denoise"

    TRAIN_TTS_URL = 'http://125.77.202.194:9398/doxvector'

    GEN_TTS_URL = 'http://125.77.202.194:9398/dotctts0'

    GET_TTS_URL = 'http://125.77.202.194:3350/dotcgettts'

    WAV_ASR_URL = 'http://125.77.202.194:9395/dotcasr'       # http://192.168.0.24:58101/dotcasr 2023年3月30日修改到9395 中闽混合版本

    #MINNAN_WAV_ASR_URL = 'http://119.3.22.24:3998/dotcasr'
    MINNAN_WAV_ASR_URL = 'http://125.77.202.194:58115/dotcasr'  #yhy2025-07-23 升级到公司的服务上 一句话闽南语模型。

    WAV_SEX_ASR_URL = 'http://119.3.22.24:3995/verifysex'

    WORD_TTS_URL = 'https://tctts.talentedsoft.com/dotctts'

    WORD_TTS_GET_WAV_PATH_URL = 'https://tctts.talentedsoft.com/tts/'
    
    BLI_TTS_GET_WAV_URL = 'http://125.77.202.194:58123/dotctts0' # yjt2025-7-29 新版中英说话人模型

    MINNAN_WORD_TTS_URL = 'https://tc.talentedsoft.com:58123/58104/dotctts'

    MINNAN_WORD_TTS_GET_WAV_PATH_URL = 'https://tc.talentedsoft.com:58123/58104/tts/'

    SEND_FILE_MAX_AGE_DEFAULT = 0  # send_file禁止缓存

    UPLOADS_DIR = os.path.join(basedir, 'uploads')
     
    UTIL_FOLDER_PATH = os.path.join(basedir, 'util') # ffmpeg工具目录

    ASR_INVALID_DIR = os.path.join(os.path.join(basedir, 'uploads'), 'asr_invalid')

    SECRET_KEY = os.getenv('SECRET_KEY', 'secret key')

    DEBUG_TB_INTERCEPT_REDIRECTS = False

    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True

    TYPE_TOKEN_MAPPING = {
        '001': 'text-related',
        '002': 'text-independent',
        '003': 'dynamic-password'
    }

    TEXT_RELATED_VOICEPRINT_TOKEN = '001'
    TEXT_RELATED_VOICEPRINT_DELETE_USER_URL = 'http://39.107.70.10:58130/deleteuser'
    TEXT_RELATED_VOICEPRINT_REGISTER_URL = 'http://39.107.70.10:58130/registeruser'
    TEXT_RELATED_VOICEPRINT_DETECTRE_GISTER_URL = 'http://39.107.70.10:58130/detectregister'
    TEXT_RELATED_VOICEPRINT_CONFIRM_URL = 'http://39.107.70.10:58130/verifymodel'
    TEXT_RELATED_VOICEPRINT_ADD_SAMPLE_URL = 'http://39.107.70.10:58130/addsample'
    TEXT_RELATED_VOICEPRINT_TRAIN_MODEL_URL = 'http://39.107.70.10:58130/trainmodel'
    TEXT_RELATED_VOICEPRINT_RECOGNITION_URL = 'http://39.107.70.10:58130/identifymodel'
    
    TEXT_RELATED = {'TOKEN': TEXT_RELATED_VOICEPRINT_TOKEN,
                    'REGISTER_URL': TEXT_RELATED_VOICEPRINT_REGISTER_URL,
                    'CONFIRM_URL': TEXT_RELATED_VOICEPRINT_CONFIRM_URL,
                    'RECOGNITION_URL': TEXT_RELATED_VOICEPRINT_RECOGNITION_URL,
                    'DETECT_REGISTER_URL': TEXT_RELATED_VOICEPRINT_DETECTRE_GISTER_URL,
                    'TRAIN_MODEL_URL': TEXT_RELATED_VOICEPRINT_TRAIN_MODEL_URL,
                    'ADD_SAMPLE_URL': TEXT_RELATED_VOICEPRINT_ADD_SAMPLE_URL,
                    'DEL_USER_URL': TEXT_RELATED_VOICEPRINT_DELETE_USER_URL}

    TEXT_INDEPENDENT_VOICEPRINT_TOKEN = '002'
    TEXT_INDEPENDENT_VOICEPRINT_DELETE_USER_URL = 'http://39.107.70.10:58130/deleteuser'
    TEXT_INDEPENDENT_VOICEPRINT_REGISTER_URL = 'http://39.107.70.10:58130/registeruser'
    TEXT_INDEPENDENT_VOICEPRINT_DETECT_REGISTER_URL = 'http://39.107.70.10:58130/detectregister'
    TEXT_INDEPENDENT_VOICEPRINT_CONFIRM_URL = 'http://39.107.70.10:58130/verifymodel'
    TEXT_INDEPENDENT_VOICEPRINT_ADD_SAMPLE_URL = 'http://39.107.70.10:58130/addsample'
    TEXT_INDEPENDENT_VOICEPRINT_TRAIN_MODEL_URL = 'http://39.107.70.10:58130/trainmodel'
    TEXT_INDEPENDENT_VOICEPRINT_RECOGNITION_URL = 'http://39.107.70.10:58130/identifymodel'
    TEXT_INDEPENDENT = {'TOKEN': TEXT_INDEPENDENT_VOICEPRINT_TOKEN,
                    'REGISTER_URL': TEXT_INDEPENDENT_VOICEPRINT_REGISTER_URL,
                    'CONFIRM_URL': TEXT_INDEPENDENT_VOICEPRINT_CONFIRM_URL,
                    'RECOGNITION_URL': TEXT_INDEPENDENT_VOICEPRINT_RECOGNITION_URL,
                    'DETECT_REGISTER_URL': TEXT_INDEPENDENT_VOICEPRINT_DETECT_REGISTER_URL,
                    'TRAIN_MODEL_URL': TEXT_INDEPENDENT_VOICEPRINT_TRAIN_MODEL_URL,
                    'ADD_SAMPLE_URL': TEXT_INDEPENDENT_VOICEPRINT_ADD_SAMPLE_URL,
                    'DEL_USER_URL': TEXT_INDEPENDENT_VOICEPRINT_DELETE_USER_URL}

    DYNAMIC_PASSWORD_VOICEPRINT_TOKEN = '003'
    DYNAMIC_PASSWORD_VOICEPRINT_DELETE_USER_URL = 'http://119.3.22.24:3999/deleteuser'
    DYNAMIC_PASSWORD_VOICEPRINT_REGISTER_URL = 'http://119.3.22.24:3999/registeruser'
    DYNAMIC_PASSWORD_VOICEPRINT_DETECT_REGISTER_URL = 'http://119.3.22.24:3999/detectregister'
    DYNAMIC_PASSWORD_VOICEPRINT_CONFIRM_URL = 'http://119.3.22.24:3999/verifymodel'
    DYNAMIC_PASSWORD_VOICEPRINT_ADD_SAMPLE_URL = 'http://119.3.22.24:3999/addsample'
    DYNAMIC_PASSWORD_VOICEPRINT_TRAIN_MODEL_URL = 'http://119.3.22.24:3999/trainmodel'
    DYNAMIC_PASSWORD_VOICEPRINT_RECOGNITION_URL = 'http://119.3.22.24:3999/identifymodel'

    DYNAMIC_PASSWORD = {'TOKEN': DYNAMIC_PASSWORD_VOICEPRINT_TOKEN,
                    'REGISTER_URL': DYNAMIC_PASSWORD_VOICEPRINT_REGISTER_URL,
                    'CONFIRM_URL': DYNAMIC_PASSWORD_VOICEPRINT_CONFIRM_URL,
                    'RECOGNITION_URL': DYNAMIC_PASSWORD_VOICEPRINT_RECOGNITION_URL,
                    'DETECT_REGISTER_URL': DYNAMIC_PASSWORD_VOICEPRINT_DETECT_REGISTER_URL,
                    'TRAIN_MODEL_URL': DYNAMIC_PASSWORD_VOICEPRINT_TRAIN_MODEL_URL,
                    'ADD_SAMPLE_URL': DYNAMIC_PASSWORD_VOICEPRINT_ADD_SAMPLE_URL,
                    'DEL_USER_URL': DYNAMIC_PASSWORD_VOICEPRINT_DELETE_USER_URL}


class DevelopmentConfig(BaseConfig):
    ENV = "development"
    DEBUG = True
    VOICE_PRESENTATION_WAV_PATH = os.path.join(basedir, 'uploads/wav_files')
    SQLALCHEMY_DATABASE_URI = prefix + os.path.join(basedir, "data.db")


class TestingConfig(BaseConfig):
    TESTING = True
    WTF_CSRF_ENABLED = False
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'  # in-memory database


class ProductionConfig(BaseConfig):
    ENV = "production"
    VOICE_PRESENTATION_WAV_PATH = os.path.join(basedir, 'uploads/wav_files')
    SQLALCHEMY_DATABASE_URI = prefix + os.path.join(basedir, "data.db")


config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}
