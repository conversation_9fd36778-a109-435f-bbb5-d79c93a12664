$(function () {
    let currentAudioFile = null;
    let audioContext = null;
    let audioBuffer = null;
    let recorder = null;
    let recording_click_sign = 0;

    // 初始化音频上下文
    function initAudioContext() {
        if (!audioContext) {
            audioContext = new(window.AudioContext || window.webkitAudioContext)();
        }
    }

    // 文件上传处理
    $('#audio-file-input').on('change', function (e) {
        const file = e.target.files[0];
        if (file) {
            handleFileUpload(file);
        }
    });

    // 拖拽上传处理
    $('#upload-area').on('dragover', function (e) {
        e.preventDefault();
        $(this).addClass('border-primary');
    });

    $('#upload-area').on('dragleave', function (e) {
        e.preventDefault();
        $(this).removeClass('border-primary');
    });

    $('#upload-area').on('drop', function (e) {
        e.preventDefault();
        $(this).removeClass('border-primary');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (isAudioFile(file)) {
                handleFileUpload(file);
            } else {
                toast(['请上传音频文件'], 1500);
            }
        }
    });

    // 检查是否为音频文件
    function isAudioFile(file) {
        const audioTypes = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/m4a', 'audio/x-m4a'];
        return audioTypes.includes(file.type) ||
            /\.(wav|mp3|m4a)$/i.test(file.name);
    }

    // 处理文件上传
    function handleFileUpload(file) {
        if (!isAudioFile(file)) {
            toast(['请上传有效的音频文件'], 1500);
            return;
        }

        currentAudioFile = file;

        // 显示文件名
        $('#audio-filename').text(file.name);

        // 创建音频URL并设置到播放器
        const audioUrl = URL.createObjectURL(file);
        $('#audio-player').attr('src', audioUrl);

        // 显示音频显示区域，隐藏上传区域
        $('#upload-area').hide();
        $('#audio-display-area').show();

        // 重置结果区域到初始状态
        $('#detection-result').hide();
        $('#detection-loading').hide();
        $('#initial-state').show();

        // 绘制波形
        drawWaveform(file);
    }

    // 绘制音频波形
    function drawWaveform(file) {
        initAudioContext();

        const reader = new FileReader();
        reader.onload = function (e) {
            audioContext.decodeAudioData(e.target.result)
                .then(function (buffer) {
                    audioBuffer = buffer;
                    renderWaveform(buffer);
                })
                .catch(function (err) {
                    console.error('音频解码失败:', err);
                    // 如果解码失败，显示简单的占位波形
                    renderPlaceholderWaveform();
                });
        };
        reader.readAsArrayBuffer(file);
    }

    // 渲染波形
    function renderWaveform(buffer) {
        const canvas = document.getElementById('waveform-canvas');
        const ctx = canvas.getContext('2d');

        // 设置canvas尺寸
        const container = $('#waveform-container');
        canvas.width = container.width();
        canvas.height = container.height();

        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 获取音频数据
        const data = buffer.getChannelData(0);
        const step = Math.ceil(data.length / canvas.width);
        const amp = canvas.height / 2;

        // 绘制波形
        ctx.fillStyle = '#007bff';
        ctx.beginPath();

        for (let i = 0; i < canvas.width; i++) {
            let min = 1.0;
            let max = -1.0;

            for (let j = 0; j < step; j++) {
                const datum = data[(i * step) + j];
                if (datum < min) min = datum;
                if (datum > max) max = datum;
            }

            ctx.fillRect(i, (1 + min) * amp, 1, Math.max(1, (max - min) * amp));
        }
    }

    // 渲染占位波形
    function renderPlaceholderWaveform() {
        const canvas = document.getElementById('waveform-canvas');
        const ctx = canvas.getContext('2d');

        const container = $('#waveform-container');
        canvas.width = container.width();
        canvas.height = container.height();

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#6c757d';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('音频波形显示', canvas.width / 2, canvas.height / 2);
    }

    // 删除音频按钮事件
    $('#delete-audio-btn').on('click', function () {
        currentAudioFile = null;
        audioBuffer = null;

        // 重置文件输入
        $('#audio-file-input').val('');

        // 清除音频播放器
        $('#audio-player').attr('src', '');

        // 显示上传区域，隐藏音频显示区域
        $('#upload-area').show();
        $('#audio-display-area').hide();

        // 重置结果区域到初始状态
        $('#detection-result').hide();
        $('#detection-loading').hide();
        $('#initial-state').show();

        // 重置录音按钮状态
        $('#recording-button').attr("hidden", true);
        $('#record-button').attr("hidden", false);
        recording_click_sign = 0;
    });

    // 开始识别按钮事件
    $('#start-detection-btn').on('click', function () {
        if (!currentAudioFile) {
            toast(['请先上传音频文件'], 1500);
            return;
        }

        // 显示加载状态，隐藏其他状态
        $('#initial-state').hide();
        $('#detection-result').hide();
        $('#detection-loading').show();

        // 设置按钮加载状态
        const button = $(this);
        const spinner = button.find('.spinner-border');
        const btnText = button.find('.btn-text');
        button.prop('disabled', true);
        spinner.removeAttr('hidden');
        btnText.text('检测中...');

        // 创建FormData并上传文件
        const formData = new FormData();
        formData.append('file', currentAudioFile);

        $.ajax({
            type: 'POST',
            url: '/ajax/voice_detection',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                $('#detection-loading').hide();

                if (data.state === 'Success') {
                    displayResult(data.result);
                } else {
                    toast([data.msg || '识别失败'], 1500);
                    // 恢复初始状态
                    $('#detection-result').hide();
                    $('#initial-state').show();
                }
            },
            error: function (xhr, status, error) {
                $('#detection-loading').hide();
                console.log('Ajax error:', xhr.responseText);
                toast(['请求异常, 请重试'], 1500);
                // 恢复初始状态
                $('#detection-result').hide();
                $('#initial-state').show();
            },
            complete: function () {
                // 恢复按钮状态
                const button = $('#start-detection-btn');
                const spinner = button.find('.spinner-border');
                const btnText = button.find('.btn-text');
                button.prop('disabled', false);
                spinner.attr('hidden', true);
                btnText.text('开始检测');
            }
        });
    });

    // 显示识别结果
    function displayResult(result) {
        // 隐藏加载状态
        $('#detection-loading').hide();

        // 设置AI生成检测结果
        const isAiGenerated = result.is_ai_generated;
        const aiResultBadge = $('#ai-result-badge');

        if (isAiGenerated) {
            aiResultBadge.text('是').removeClass().addClass('badge badge-danger');
        } else {
            aiResultBadge.text('否').removeClass().addClass('badge badge-success');
        }

        // 设置置信度
        $('#confidence-value').text(result.confidence.toFixed(2));

        // 设置处理时间
        $('#processing-time').text(result.processing_time_ms.toFixed(1));

        // 显示结果
        $('#detection-result').show();
    }

    // 录音按钮点击事件
    $('#record-button').on('click', function () {
        if (recorder == null) {
            recorder = Recorder({
                type: 'wav',
                bitRate: 16, // 采样位数，支持 8 或 16，默认是16
                sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000
                onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate) {
                    // 处理录音数据
                }
            })
        }

        var t = setTimeout(function () {
            console.log("无法录音：权限请求被忽略!");
            toast(['无法录音：权限请求被忽略'], 2000);
        }, 8000);

        recorder.open(function () { // 打开麦克风授权获得相关资源
            clearTimeout(t);
            recorder.start(); // 开始录音
            $('#recording-button').attr("hidden", false); // 显示停止录音按钮
            $('#record-button').attr("hidden", true); // 隐藏开始录音按钮
        }, function (msg, isUserNotAllow) {
            clearTimeout(t);
            console.log((isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg);
            toast([(isUserNotAllow ? "用户拒绝授权，" : "") + "无法录音: " + msg], 2000);
        });
    });

    // 停止录音按钮点击事件
    $('#recording-button').on('click', function () {
        if (recording_click_sign === 1) { // 如果当前事件未结束时又被触发点击事件，则退出事件
            return toast(['请不要重复点击停止按钮'], 1500);
        }

        recording_click_sign = 1; // 第一次开始本事件后，置为1，表示事件已被触发，再次点击将不再触发。(事件结尾重置)

        recorder.stop(function (blob, duration) {
            $('#recording-button').attr("hidden", true); // 隐藏停止录音按钮
            $('#record-button').attr("hidden", false); // 显示开始录音按钮

            // 处理录音完成后的逻辑
            handleRecordingComplete(blob, duration);

            recording_click_sign = 0; // 重置点击标志
        }, function (msg) {
            console.log("录音失败:" + msg);
            toast(['录音失败: ' + msg], 2000);
            $('#recording-button').attr("hidden", true);
            $('#record-button').attr("hidden", false);
            recording_click_sign = 0;
        });
    });

    // 处理录音完成
    function handleRecordingComplete(blob, duration) {
        // 创建文件对象
        const recordedFile = new File([blob], `recorded_${Date.now()}.wav`, {
            type: 'audio/wav'
        });

        // 设置当前音频文件
        currentAudioFile = recordedFile;

        // 显示文件名
        $('#audio-filename').text(recordedFile.name);

        // 创建音频URL并设置到播放器
        const audioUrl = URL.createObjectURL(blob);
        $('#audio-player').attr('src', audioUrl);

        // 显示音频显示区域，隐藏上传区域
        $('#upload-area').hide();
        $('#audio-display-area').show();

        // 重置结果区域到初始状态
        $('#detection-result').hide();
        $('#detection-loading').hide();
        $('#initial-state').show();

        // 绘制波形
        drawWaveform(recordedFile);

        toast(['录音完成，时长: ' + duration + '秒'], 2000);
    }

    // 窗口大小改变时重新绘制波形
    $(window).on('resize', function () {
        if (audioBuffer && $('#audio-display-area').is(':visible')) {
            setTimeout(function () {
                renderWaveform(audioBuffer);
            }, 100);
        }
    });
});