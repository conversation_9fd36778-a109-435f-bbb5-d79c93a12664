name: asvspoof
channels:
  - conda-forge
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - backcall=0.2.0=pyh9f0ad1d_0
  - bzip2=1.0.8=h7f98852_4
  - ca-certificates=2024.8.30=hbcca054_0
  - debugpy=1.6.7=py38h6a678d5_0
  - decorator=5.1.1=pyhd8ed1ab_0
  - entrypoints=0.4=pyhd8ed1ab_0
  - ffmpeg=4.2.2=h20bf706_0
  - freetype=2.10.4=h0708190_1
  - gmp=6.1.2=hf484d3e_1000
  - gnutls=3.6.15=he1e5248_0
  - importlib_metadata=7.0.1=hd3eb1b0_0
  - ipykernel=6.14.0=py38h7f3c49e_0
  - ipython=7.33.0=py38h578d9bd_0
  - jedi=0.18.2=pyhd8ed1ab_0
  - jupyter_client=7.0.6=pyhd8ed1ab_0
  - jupyter_core=5.7.2=pyh31011fe_1
  - lame=3.100=h7f98852_1001
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.3=he6710b0_2
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libidn2=2.3.4=h5eee18b_0
  - libopus=1.3.1=h7f98852_1
  - libpng=1.6.39=h5eee18b_0
  - libsodium=1.0.18=h36c2ea0_1
  - libstdcxx-ng=13.2.0=hc0a3c3a_7
  - libtasn1=4.19.0=h5eee18b_0
  - libunistring=0.9.10=h7f98852_0
  - libvpx=1.7.0=h439df22_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_0
  - nettle=3.7.3=hbbd107a_1
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1l=h7f98852_0
  - parso=0.8.4=pyhd8ed1ab_0
  - pexpect=4.9.0=pyhd8ed1ab_0
  - pickleshare=0.7.5=py_1003
  - pip=24.0=py38h06a4308_0
  - prompt-toolkit=3.0.48=pyha770c72_0
  - ptyprocess=0.7.0=pyhd3deb0d_0
  - pygments=2.18.0=pyhd8ed1ab_0
  - python=3.8.12=h12debd9_0
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python_abi=3.8=2_cp38
  - pyzmq=19.0.2=py38ha71036d_2
  - readline=8.2=h5eee18b_0
  - setuptools=69.5.1=py38h06a4308_0
  - six=1.16.0=pyh6c4a22f_0
  - sqlite=3.38.2=hc218d9a_0
  - tk=8.6.11=h1ccaba5_0
  - tornado=6.1=py38h0a891b7_3
  - traitlets=5.14.3=pyhd8ed1ab_0
  - wcwidth=0.2.13=pyhd8ed1ab_0
  - wheel=0.43.0=py38h06a4308_0
  - x264=1!157.20191217=h7b6447c_0
  - xz=5.4.6=h5eee18b_1
  - zeromq=4.3.5=h6a678d5_0
  - zlib=1.2.13=h5eee18b_1
  - pip:
    - absl-py==2.1.0
    - accelerate==0.31.0
    - adapters==1.0.1
    - aiofiles==23.2.1
    - aiohttp==3.9.5
    - aiosignal==1.3.1
    - annotated-types==0.7.0
    - anyio==4.5.2
    - argcomplete==3.5.1
    - arrow==1.3.0
    - async-timeout==4.0.3
    - attrs==23.2.0
    - audioread==3.0.1
    - beartype==0.19.0
    - beautifulsoup4==4.12.3
    - bleach==6.1.0
    - bokeh==3.1.1
    - boto3==1.34.126
    - botocore==1.34.126
    - bravado==11.0.3
    - bravado-core==6.1.1
    - cachetools==5.3.3
    - certifi==2024.6.2
    - cffi==1.16.0
    - charset-normalizer==3.3.2
    - clarabel==0.9.0
    - click==8.1.7
    - cloudpickle==3.1.0
    - cmeel==0.56.0
    - colorcet==3.1.0
    - contourpy==1.1.1
    - cvxopt==1.3.2
    - cycler==0.12.1
    - daqp==0.6.0
    - dask==2023.5.0
    - datasets==2.20.0
    - datashader==0.15.2
    - datashape==0.5.2
    - dill==0.3.8
    - docker-pycreds==0.4.0
    - ecos==2.0.14
    - einops==0.8.0
    - encodec==0.1.1
    - exceptiongroup==1.2.2
    - fastapi==0.115.11
    - ffmpy==0.5.0
    - fonttools==4.53.0
    - fqdn==1.5.1
    - frozenlist==1.4.1
    - gdown==5.2.0
    - gitdb==4.0.11
    - gitpython==3.1.43
    - google-auth==2.30.0
    - google-auth-oauthlib==1.0.0
    - gputil==1.4.0
    - grad-cam==1.5.4
    - gradio==4.44.0
    - gradio-client==1.3.0
    - grpcio==1.64.1
    - h11==0.14.0
    - h5py==3.11.0
    - highspy==1.9.0
    - holoviews==1.17.1
    - httpcore==1.0.7
    - httpx==0.28.1
    - huggingface-hub==0.23.3
    - idna==3.7
    - imageio==2.35.1
    - importlib-metadata==7.1.0
    - importlib-resources==6.4.0
    - install==1.3.5
    - isoduration==20.11.0
    - jax==0.4.13
    - jaxlib==0.4.13
    - jmespath==1.0.1
    - joblib==1.4.2
    - jsonpointer==3.0.0
    - jsonref==1.1.0
    - jsonschema==4.22.0
    - jsonschema-specifications==2023.12.1
    - julius==0.2.7
    - kaldiio==2.18.0
    - kiwisolver==1.4.5
    - lazy-loader==0.4
    - librosa==0.10.2.post1
    - lightning-utilities==0.11.9
    - linkify-it-py==2.0.3
    - lion-pytorch==0.2.2
    - llvmlite==0.41.1
    - locket==1.0.0
    - markdown==3.6
    - markdown-it-py==3.0.0
    - matplotlib==3.7.5
    - mdit-py-plugins==0.4.2
    - mdurl==0.1.2
    - ml-dtypes==0.2.0
    - monotonic==1.6
    - msgpack==1.0.8
    - multidict==6.0.5
    - multipledispatch==1.0.0
    - multiprocess==0.70.16
    - munch==4.0.0
    - neptune-client==1.10.4
    - numba==0.58.1
    - numpy==1.24.4
    - nvidia-cublas-cu11==**********
    - nvidia-cuda-nvrtc-cu11==11.7.99
    - nvidia-cuda-runtime-cu11==11.7.99
    - nvidia-cudnn-cu11==********
    - oauthlib==3.2.2
    - opencv-python==*********
    - opt-einsum==3.4.0
    - orjson==3.10.15
    - osqp==0.6.7.post3
    - packaging==24.1
    - pandas==2.0.3
    - panel==1.2.3
    - parallel-wavegan==0.6.1
    - param==2.1.1
    - partd==1.4.1
    - peft==0.13.2
    - pillow==10.4.0
    - piqp==0.4.2
    - pkgutil-resolve-name==1.3.10
    - platformdirs==4.2.2
    - pooch==1.8.2
    - primepy==1.3
    - protobuf==3.20.1
    - proxsuite==0.6.7
    - psutil==5.9.8
    - pyarrow==16.1.0
    - pyarrow-hotfix==0.6
    - pyasn1==0.6.0
    - pyasn1-modules==0.4.0
    - pycparser==2.22
    - pyct==0.5.0
    - pydantic==2.8.2
    - pydantic-core==2.20.1
    - pydub==0.25.1
    - pyjwt==2.8.0
    - pynndescent==0.5.13
    - pyparsing==3.1.2
    - pysocks==1.7.1
    - python-multipart==0.0.20
    - pytorch-model-summary==0.1.2
    - pytorchcv==0.0.67
    - pytz==2020.1
    - pyviz-comms==3.0.3
    - pywavelets==1.4.1
    - pyyaml==6.0.1
    - qdldl==0.1.7.post5
    - qpalm==1.2.5
    - qpax==0.0.9
    - qpsolvers==4.4.0
    - quadprog==0.1.12
    - referencing==0.35.1
    - regex==2024.5.15
    - requests==2.32.3
    - requests-oauthlib==2.0.0
    - rfc3339-validator==0.1.4
    - rfc3986-validator==0.1.1
    - rich==13.9.4
    - rpds-py==0.18.1
    - rsa==4.9
    - ruff==0.11.0
    - s3transfer==0.10.1
    - safetensors==0.4.3
    - scikit-image==0.21.0
    - scikit-learn==1.3.2
    - scipy==1.10.1
    - scs==3.2.7.post2
    - seaborn==0.13.2
    - semantic-version==2.10.0
    - sentry-sdk==2.5.1
    - setproctitle==1.3.3
    - shellingham==1.5.4
    - simplejson==3.19.2
    - smmap==5.0.1
    - sniffio==1.3.1
    - soundfile==0.12.1
    - soupsieve==2.6
    - soxr==0.3.7
    - speechtokenizer==1.0.1
    - starlette==0.44.0
    - swagger-spec-validator==3.0.3
    - tensorboard==2.14.0
    - tensorboard-data-server==0.7.2
    - tensorboardx==2.4.1
    - threadpoolctl==3.5.0
    - tifffile==2023.7.10
    - tokenizers==0.20.3
    - tomli==2.2.1
    - tomlkit==0.12.0
    - toolz==1.0.0
    - torch==1.13.0
    - torch-audiomentations==0.11.1
    - torch-lr-finder==0.2.1
    - torch-pitch-shift==1.2.4
    - torch-tb-profiler==0.2.0
    - torchaudio==0.13.0
    - torchcontrib==0.0.2
    - torchinfo==1.8.0
    - torchmetrics==1.5.2
    - torchsummary==1.5.1
    - torchvision==0.14.0
    - tqdm==4.66.4
    - transformers==4.45.2
    - ttach==0.0.3
    - typer==0.15.2
    - types-python-dateutil==2.9.0.20240316
    - typing-extensions==4.12.2
    - tzdata==2024.1
    - uc-micro-py==1.0.3
    - umap-learn==0.5.7
    - uri-template==1.3.0
    - urllib3==2.2.3
    - uvicorn==0.33.0
    - wandb==0.17.1
    - webcolors==24.6.0
    - webencodings==0.5.1
    - websocket-client==1.8.0
    - websockets==12.0
    - werkzeug==3.0.3
    - xarray==2023.1.0
    - xmltodict==0.14.2
    - xxhash==3.4.1
    - xyzservices==2024.9.0
    - yarl==1.9.4
    - yq==3.4.3
    - zipp==3.19.2
prefix: /root/anaconda3/envs/asvspoof
