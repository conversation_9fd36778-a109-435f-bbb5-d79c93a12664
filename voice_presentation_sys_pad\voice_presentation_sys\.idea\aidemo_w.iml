<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/voice_presentation_sys" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/list" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.8 (voice_presentation_sys)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="TemplatesService">
    <option name="TEMPLATE_CONFIGURATION" value="Jinja2" />
    <option name="TEMPLATE_FOLDERS">
      <list>
        <option value="$MODULE_DIR$/aidemo/templates" />
      </list>
    </option>
  </component>
</module>