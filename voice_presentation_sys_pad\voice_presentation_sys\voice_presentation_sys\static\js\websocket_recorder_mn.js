var testOutputWavLog=false;//本测试如果是输出mp3，就顺带打一份wav的log，录音后执行mp3、wav合并的demo代码可对比音质
var testSampleRate=16000;
var testBitRate=16;
var SendInterval=100;//这玩意如果刚开始就定好16k16bitwav的话 说是调成0都可以。。？？

var GetArrayLen=9600;  //那就每次置2位的buffer为null好了 每次识别会留1408

var count=1; //后台需求保存文件时的文件序号变量(测试用)
/******
 转码发送间隔（实际间隔比这个变量值偏大点，取决于BufferSize）。这个值可以设置很大，但不能设置很低，毕竟转码和传输还是要花费一定时间的，设备性能低下可能还处理不过来。
 mp3格式下一般大于500ms就能保证能够正常转码处理，wav大于100ms，剩下的问题就是传输速度了。由于转码操作都是串行的，录制过程中转码生成出来mp3顺序都是能够得到保证，但结束时最后几段数据可能产生顺序问题，需要留意。由于传输通道不一定稳定，后端接收到的顺序可能错乱，因此可以携带编号进行传输，完成后进行一次排序以纠正顺序错乱的问题。
 mp3格式在间隔太低的情况下中间的停顿会非常明显，可适当调大间隔以规避此影响，因为mp3编码时首尾出现了填充的静默数据（mp3.js编码器内已尽力消除了这些静默，但还是会有些许的静默停顿）；wav格式没有此问题，测试时可以打开 testOutputWavLog + mp3、wav合并demo 来对比音质。
 当出现性能问题时，可能音频编码不过来，将采取丢弃部分帧的策略。
 ******/


$('#stream-record-button-mn').on('click', function () {
    $('#stream-record-button-mn').attr("hidden", true);
    $('#stream-recording-button-mn').attr("hidden", false);
    // create_websocket();  //ajax后端创建websocket
    recStartWav();
});

$('#stream-recording-button-mn').on('click', function () {
    $('#stream-recording-button-mn').attr("hidden", true);
    $('#stream-record-button-mn').attr("hidden", false);
    recStop();
    // delete_websocket(); //ajax后端删除websocket
});


//调用录音
var rec;
var ws;
var sid = parseInt(Math.random()*3000).toString();  // 每个用户取3000内的随机值

function recStartWav(){
    recStart("pcm");
}
function recStart(type){
    if(rec){
        rec.close();
    }

    if(ws){  //这里直接判断ws好像有点问题
        ws.close();  // 如果本来有连接就先关掉
    }
    //开一个WebSocket对象
    ws = new WebSocket("wss://tc.talentedsoft.com:58124/wss16kmn/dotcwsasr?userid=test123&token=test1&sid=" + sid)
    // ws = new WebSocket("wss://tc.talentedsoft.com:58124/send_binary")
    all_webSocket_callback(ws);  //注册websocket回调

    rec=Recorder({
        type:type,  // 输出类型
        bitRate: 16,  //比特率
        sampleRate: 16000,  //采样率
        onProcess:function(buffers,powerLevel,bufferDuration,bufferSampleRate){
            RealTimeSendTry(rec, false);//推入实时处理，因为是unknown格式，这里简化函数调用，没有用到buffers和bufferSampleRate，因为这些数据和rec.buffers是完全相同的。
        }
    });

    var t=setTimeout(function(){
        console.log("无法录音：权限请求被忽略!");
    },8000);

    rec.open(function(){//打开麦克风授权获得相关资源
        clearTimeout(t);
        rec.start();//开始录音

        RealTimeSendTryReset(type);//重置环境变量
    },function(msg,isUserNotAllow){
        clearTimeout(t);
        console.log((isUserNotAllow?"UserNotAllow，":"")+"无法录音:"+msg);
    });
}
function recStop(){
    RealTimeSendTry(rec,true);//最后一次发送
    rec.close();//直接close掉即可，这个例子不需要获得最终的音频文件(意思是不用stop)
	ws.send("end")
    ws.close();//关闭websocket
    $('#voice-content2').val("")  // 清空避免残留
}

//重置环境
var RealTimeSendTryReset=function(type){
    realTimeSendTryType=type;
    realTimeSendTryTime=0;
    count = 1
};

var realTimeSendTryType;
var realTimeSendTryEncBusy;
var realTimeSendTryTime=0;
var realTimeSendTryNumber;
var transferUploadNumberMax;
var realTimeSendTryChunk;

//=====实时处理核心函数==========
var RealTimeSendTry=function(rec, isClose){
    var t1=Date.now(), endT=0, recImpl=Recorder.prototype;
    if(realTimeSendTryTime==0){
        realTimeSendTryTime=t1;  //第一次回调时把t1赋值给realTimeSendTryTime
        realTimeSendTryEncBusy=0;  //这啥？编码队列里任务的数量？
        realTimeSendTryNumber=0;
        transferUploadNumberMax=0;
        realTimeSendTryChunk=null;
    }
    if(!isClose && t1-realTimeSendTryTime<SendInterval){  //t1(每次回调获得的最新时间)-realTimeSendTryTime(第一次回调获得的时间)
        return;//控制缓冲达到指定间隔才进行传输
    }
    realTimeSendTryTime=t1;  // 每经过 指定的间隔时间后 就把 当次成功回调的时间 更新给 realTimeSendTryTime
    var number=++realTimeSendTryNumber;  // 先++再赋值 回调事件成功进行次数+1

    //借用SampleData函数进行数据的连续处理，采样率转换是顺带的
    var chunk=Recorder.SampleData(rec.buffers,rec.srcSampleRate,testSampleRate,realTimeSendTryChunk,{frameType:isClose?"":realTimeSendTryType});
    //清理已处理完的缓冲数据，释放内存以支持长时间录音，最后完成录音时不能调用stop，因为数据已经被清掉了
    for(var i=realTimeSendTryChunk?realTimeSendTryChunk.index:0;i<chunk.index;i++){
        rec.buffers[i]=null;
    }
    realTimeSendTryChunk=chunk;
    //没有新数据，或结束时的数据量太小，不能进行mock转码
    if(chunk.data.length==0 || isClose&&chunk.data.length<2000){
        TransferUpload(number,null,0,null,isClose, count);
        return;
    }

    //实时编码队列阻塞处理
    if(!isClose){
        if(realTimeSendTryEncBusy>=2){
            console.log("编码队列阻塞，已丢弃一帧",1);
            return;
        }
    }
    realTimeSendTryEncBusy++;

    //通过mock方法实时转码成mp3、wav
    var encStartTime=Date.now();
    var recMock=Recorder({
        type:realTimeSendTryType
        ,sampleRate:testSampleRate //采样率
        ,bitRate:testBitRate //比特率
    });
    recMock.mock(chunk.data,chunk.sampleRate);
    recMock.stop(function(blob,duration){
        realTimeSendTryEncBusy&&(realTimeSendTryEncBusy--);  // 等价于 if (realTimeSendTryEncBusy) { realTimeSendTryEncBusy-- }
        blob.encTime=Date.now()-encStartTime;
        //转码好就推入传输
        TransferUpload(number,blob,duration,recMock,isClose, count);
        count = count + 1
    },function(msg){
        realTimeSendTryEncBusy&&(realTimeSendTryEncBusy--);

        //转码错误？没想到什么时候会产生错误！
        console.log("不应该出现的错误:"+msg,1);
    });
};

//=====数据传输函数==========  //多加了一个count来给文件一个后缀命名
var TransferUpload=function(number,blobOrNull,duration,blobRec,isClose,count){
    transferUploadNumberMax=Math.max(transferUploadNumberMax,number);
    if(blobOrNull){
        var blob=blobOrNull;
        var encTime=blob.encTime;
        //*********Blob***************
        ws.send(blob) //发送要传输的blob到队列中

        var numberFail=number<transferUploadNumberMax?'<span style="color:red">顺序错乱的数据，如果要求不高可以直接丢弃，或者调大SendInterval试试</span>':"";
        var logMsg="No."+(number<100?("000"+number).substr(-3):number)+numberFail;

        // console.log(blob,duration,blobRec,logMsg+"花"+("___"+encTime).substr(-3)+"ms");

        if(true && number%100==0){
            // 先啥也不做吧
        }
    }

    if(isClose){
        console.log("No."+(number<100?("000"+number).substr(-3):number)+":已停止传输");
    }
};

var voice_content = $('#voice-content')
var voice_content2 = $('#voice-content2')
// 一个放WebSocket回调函数的地方
function all_webSocket_callback(ws){
    ws.onopen = function(){
        console.log("WebSocket连接创建成功")
        // ws.send("stream_asr")  // 先发一个socket_id标识
    }
    ws.onmessage = function(ret){
        let newStr = ret.data.replace(/\n/g, "~");
        let ret_json = JSON.parse(newStr)
        console.log("ret_json: ", ret_json)
        let errCode = ret_json.errCode
        let content = ret_json.result
        console.log(content)
        if (content != ""){
            if (errCode == "3"){
                voice_content2.val(content)
            }
            else if (errCode == "0"){
                let date = new Date()                
                let newcontent = content.replace(/~/g, "\n");
                newcontent = newcontent.replace("|", " ");
                newcontent = newcontent.replace("|", " ");
                newcontent = newcontent.replace("|", " ");
                newcontent = newcontent.replace("|", " ");
                //voice_content.append("\n"+newcontent);
                voice_content.append("【"+ dateFormat("YYYY-mm-dd HH:MM", date) + "】", content + " " + "\r\n");
                voice_content.scrollTop(voice_content[0].scrollHeight)  //设置这个可以自动下拉
                voice_content2.val("")  
            }
            else {
                console.log(ret_json)
            }
        }
    }
    // ws.onmessage = function(ret){
    //     let ret_json = JSON.parse(ret.data)
    //     if (ret_json.state == 'Error'){  //只有Success才能继续下去
    //         return
    //     }
    //     let errCode = ret_json.content.errCode
    //     let content = ret_json.content.result
    //
    //     if (content != ""){
    //         if (errCode == "3"){
    //             voice_content2.val(content)
    //         }
    //         else if (errCode == "0"){
    //             let date = new Date()
    //             // voice_content.append("【"+ dateFormat("YYYY-mm-dd HH:MM", date) + "】", content + " " + "&#10;");
    //             voice_content.append("【"+ dateFormat("YYYY-mm-dd HH:MM", date) + "】", content + " " + "\r\n");
    //             voice_content.scrollTop(voice_content[0].scrollHeight)  //设置这个可以自动下拉
    //             voice_content2.val("")
    //         }
    //         else {
    //             console.log(ret_json)
    //         }
    //     }
    // }

    ws.onclose = function(){
        console.log("WebSocket连接已关闭")
    }

    ws.onerror = function (e) {
        console.log("连接出错。" + e)
    }
}

function dateFormat(fmt, date) {
    let ret;
    const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
    };
    for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
            fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        }
    }
    return fmt;
}

//中文16k
// socket_id: "stream_asr"
// dest_ws: "ws://192.168.0.24:58101/dotcwsasr?userid=ts_demo&token=ts_demo&sid=301"
// function create_websocket(){
//     $.ajax({
//         type: 'GET',
//         url: '/ws/create_websocket',
//         data: { socket_id: "stream_asr", dest_ws: "ws://192.168.0.24:58101/dotcwsasr?userid=ts_demo&token=ts_demo&sid=" + sid},
//         success: function (data) {
//             if (data.state == 'Success') {
//                 console.log("Success: ", data.content)
//             }
//             else {
//                 console.log("Error: ", data.content)
//             }
//         },
//     });
// }
//
// function delete_websocket(){
//     $.ajax({
//         type: 'GET',
//         url: '/ws/del_websocket',
//         data: { socket_id: "stream_asr" },
//         success: function (data) {
//             if (data.state == 'Success') {
//                 console.log("Success: ", data.content)
//             }
//             else {
//                 console.log("Error: ", data.content)
//             }
//         },
//     });
// }