$(function () {
    var upload_wav = false
    var denoised_wav_url = ""
    var denoised_wav_ready = false // 标记降噪音频是否已加载完成


    $('#submit-file').on('click', function () {

        // 获取表单对象
        let fm = $('#upload-form');
        let spin = $("#spinner")
        let formData = new FormData(fm[0]);
        console.log(fm)
        wavesurfer1.loadBlob(fm[0][0].files[0]) // 直接加载File或是Blob对象，console.log进去一层一层找
        upload_wav = true
        formData.append("file_type", "upload_file"); // 因后台使用同一个处理函数，所以此处作为一个标记。
        spin.removeAttr("hidden"); //spinner默认是Hidden的，事件开始需要显示出来。
        toast(['文件上传成功，正在进行降噪处理'], 2000)

        $.ajax({
            type: 'POST',
            url: '/ajax/denoise',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                spin.attr("hidden", "hidden") //加载动画隐藏
                if (data.state === 'Success') {
                    denoised_wav_url = "/ajax/denoise/wav?wavfile=" + data.msg
                    denoised_wav_ready = false // 重置标志变量
                    wavesurfer2.empty() // 新上传文件成功后，把之前文件的波形清除
                    toast(['降噪处理完成，正在加载音频波形'], 2000)
                    console.log("新的wav_url: ", denoised_wav_url)

                    // 自动加载降噪后的音频波形，但不播放
                    loadDenoiseAudio()
                } else {
                    toast([data.msg], 2000); //toast弹窗显示错误信息
                }
            },
            error: function () {
                spin.attr("hidden", "hidden") //加载动画隐藏
                toast(['服务异常'], 2000)
            }
        });
    })

    var wavesurfer1 = WaveSurfer.create({
        container: '#waveform1'
    });

    var wavesurfer2 = WaveSurfer.create({
        container: '#waveform2'
    });

    // 回调注册
    wavesurfer1_event_load(wavesurfer1)
    wavesurfer2_event_load(wavesurfer2)


    $('#play-button1').on('click', function (e) {
        if (!upload_wav) {
            toast(["暂无语音可以播放，请上传文件后尝试。"], 1500)
            return
        }
        wavesurfer1.play()
    })

    $('#loading-button1').on('click', function (e) {
        let play_button = $('#play-button1');
        let load_button = $('#loading-button1');

        play_button.attr("hidden", false);
        load_button.attr("hidden", true);

        // 停止播放并回到波形开头
        wavesurfer1.stop()
    })

    // 加载降噪音频的函数（只加载波形，不播放）
    function loadDenoiseAudio() {
        if (denoised_wav_url == "") {
            return
        }

        // 直接尝试加载音频波形
        // 如果文件还没生成，wavesurfer会触发error事件，我们在那里处理重试
        wavesurfer2.load(denoised_wav_url)
    }

    $('#play-button2').on('click', function (e) {
        if (!denoised_wav_ready) {
            toast(["请先上传音频文件进行降噪处理"], 1500)
            return
        }

        // 播放降噪后的音频
        wavesurfer2.play()
    })

    $('#loading-button2').on('click', function (e) {
        let play_button = $('#play-button2');
        let load_button = $('#loading-button2');

        play_button.attr("hidden", false);
        load_button.attr("hidden", true);

        // 停止播放并回到波形开头
        wavesurfer2.stop()
    })

    // 第一个波形图
    function wavesurfer1_event_load(w) {
        let play_button = $('#play-button1');
        let load_button = $('#loading-button1');
        // 播放开始
        w.on('play', function () {
            play_button.attr("hidden", true);
            load_button.attr("hidden", false);
        });
        // 播放结束
        w.on('finish', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
        });
        // 发生错误
        w.on('error', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
            console.log("波形图加载异常。")
        });
    }

    // 第二个波形图
    function wavesurfer2_event_load(w) {
        let play_button = $('#play-button2');
        let load_button = $('#loading-button2');
        // 绘制完波形之后
        w.on('ready', function () {
            // 波形加载完成，显示播放按钮，但不自动播放
            denoised_wav_ready = true // 标记音频已准备好
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
            toast(["音频波形加载完成，可以播放降噪后的音频"], 1500)
        });
        // 播放开始
        w.on('play', function () {
            play_button.attr("hidden", true);
            load_button.attr("hidden", false);
        });
        // 播放结束
        w.on('finish', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
        });
        // 发生错误
        w.on('error', function (e) {
            denoised_wav_ready = false // 标记音频未准备好
            console.log("波形图加载异常：", e)

            // 如果是因为文件还没生成完成导致的错误，自动重试
            if (denoised_wav_url != "") {
                setTimeout(function () {
                    console.log("重试加载降噪音频...")
                    loadDenoiseAudio()
                }, 2000) // 2秒后重试
            } else {
                play_button.attr("hidden", false);
                load_button.attr("hidden", true);
                toast(["音频加载失败，请稍后重试"], 1500)
            }
        });
    }
})