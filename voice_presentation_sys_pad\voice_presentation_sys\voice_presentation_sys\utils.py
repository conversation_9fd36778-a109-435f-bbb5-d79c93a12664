# -*- coding: utf-8 -*-
try:
    from urlparse import urlparse, urljoin
except ImportError:
    from urllib.parse import urlparse, urljoin

import os
import requests
from flask import request, redirect, url_for


def is_safe_url(target):  # 验证目标url是否为内部url
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and ref_url.netloc == test_url.netloc


def redirect_back(default='voice_presentation.index', **kwargs):  # 重定向函数
    for target in request.args.get('next'), request.referrer:  # referer是一个用来记录请求发源地址的HTTP首部字段
        if not target:
            continue
        if is_safe_url(target):
            return redirect(target)
    return redirect(url_for(default, **kwargs))


def make_dir_if_not(dir_path):
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)


def general_requests_func(url, userid, token, file=None, content=None):
    if not file:
        res_json = requests.post(url=url,data={'userid': userid, 'token': token}).json()
    else:
        if content==None:
            res_json = requests.post(url=url, data={'userid': userid, 'token': token, 'content': 123456}, files={'file': file}).json()            
        else:
            res_json = requests.post(url=url, data={'userid': userid, 'token': token, 'content': content}, files={'file': file}).json()#yhy2025-03-27
    return res_json
