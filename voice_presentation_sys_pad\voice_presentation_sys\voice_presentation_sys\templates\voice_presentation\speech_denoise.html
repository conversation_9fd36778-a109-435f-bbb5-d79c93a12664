{% extends 'base.html' %}

{% block styles %}
    {{ super() }}
{% endblock styles %}

{% block content %}
    <div class="container-fluid container-fluid-padding">
        <div class="row">
            <div class="col content-area-container2">
                <div class="bottom-container2">
                    <button id="play-button1" type="button" class="btn btn-primary" style="margin-right: 30px">
                        开始播放
                        <span class="fi-caret-right"></span>
                    </button>
                    <button id="loading-button1" type="button" class="btn btn-primary" style="margin-right: 30px" hidden>
                        正在播放
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>

                    <button id="upload-wav-button" type="button" class="btn btn-light upload-btn" data-toggle="modal"
                            data-target="#confirm-upload">
                        上传文件
                        <span class="fi-cloud-upload"></span>
                    </button>
                    <div id="spinner" class="spinner-border spinner-border-sm float-right" role="status" hidden="hidden">
                        <span class="sr-only"></span>
                    </div>
                </div>
                <div id="waveform1" class="wave-form"></div>
            </div>
            <div class="col content-area-container2">
                <div class="bottom-container2">
                    <button id="play-button2" type="button" class="btn btn-primary float-right">
                        开始播放
                        <span class="fi-caret-right"></span>
                    </button>
                    <button id="loading-button2" type="button" class="btn btn-primary float-right" hidden>
                        正在播放
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>
                </div>
                <div id="waveform2" class="wave-form"></div>
            </div>
        </div>
    </div>
    <!-- 文件上传模态框 -->
    <div class="modal fade" id="confirm-upload" tabindex="-1" role="dialog" aria-labelledby="文件上传" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="upload-form" enctype="multipart/form-data">
                        <input id="file"  type="file"  name="wav_file" required accept=".wav">
                        <input id="submit-file" type="button" aria-hidden="true" data-dismiss="modal"
                               aria-label="Close" class="btn btn-secondary" value="提交">
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div align="center" style="font-size:16px;color:#000"> &nbsp;&nbsp;&nbsp;&nbsp;    联系电话: 0592-5998812 &nbsp;&nbsp;&nbsp;&nbsp;        
     <a href="https://www.talentedsoft.com/" style=" color:#000;">公司网站链接</a> &nbsp;&nbsp;    
    </div>    
{% endblock content %}

{% block scripts %}
    {{ super() }}
    <script src="https://unpkg.com/wavesurfer.js"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='js/speech_denoise.js') }}"></script>
{% endblock scripts %}
