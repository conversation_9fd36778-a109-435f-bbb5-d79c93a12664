{% extends 'base.html' %} {% block content %}
<div class="container p-4">
  <!-- 使用说明 -->
  <div class="alert alert-info">
    <h5>使用说明</h5>
    <ul class="mb-0">
      <li>
        点击"选择文件"或直接拖拽音频文件到上传区域，支持 WAV、MP3、M4A 格式。
      </li>
      <li>上传完成后可以预览音频波形，点击"开始检测"进行AI生成音频检测。</li>
      <li>检测结果将显示音频是否为AI生成、置信度和处理时间等信息。</li>
    </ul>
  </div>

  <!-- 音频处理模块 -->
  <div class="card">
    <div class="card-body">
      <!-- 音频上传区域 -->
      <div class="mb-3">
        <label class="form-label"
          ><strong>在这里上传待检测的音频文件：</strong></label
        >

        <!-- 文件上传区域 -->
        <div
          class="upload-area border rounded mb-3 p-4 text-center"
          id="upload-area"
        >
          <div id="upload-placeholder">
            <i
              class="fi-cloud-upload"
              style="font-size: 3rem; color: #6c757d"
            ></i>
            <p class="mt-2 mb-0">点击上传音频文件、拖拽文件到此处或在线录音</p>
            <p class="text-muted small">支持 WAV, MP3, M4A 格式</p>
            <input
              type="file"
              id="audio-file-input"
              accept=".wav,.mp3,.m4a"
              style="display: none"
            />
            <div class="d-flex justify-content-center flex-wrap">
              <button
                type="button"
                class="btn btn-outline-primary mr-3"
                onclick="document.getElementById('audio-file-input').click()"
              >
                选择文件 <span class="fi-cloud-upload"></span>
              </button>
              <button
                id="record-button"
                class="btn btn-primary"
                data-type="detection"
              >
                开始录音 <span class="fi-microphone"></span>
              </button>
              <button hidden id="recording-button" class="btn btn-primary">
                停止录音
                <div
                  class="spinner-border spinner-border-sm ml-2"
                  role="status"
                >
                  <span class="sr-only"></span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- 音频播放和波形显示区域 -->
        <div id="audio-display-area" style="display: none">
          <div
            class="d-flex align-items-center justify-content-between flex-wrap mb-3"
          >
            <!-- 音频播放器 -->
            <audio
              id="audio-player"
              controls
              class="flex-grow-1 mr-3"
              style="min-width: 200px"
            >
              您的浏览器不支持音频播放
            </audio>

            <!-- 文件名显示 -->
            <div class="mt-2 mt-md-0">
              <small class="text-muted" id="audio-filename"></small>
            </div>
          </div>

          <!-- 波形显示区域 -->
          <div
            id="waveform-container"
            class="border rounded p-2 mb-3"
            style="height: 120px; background-color: #f8f9fa"
          >
            <canvas id="waveform-canvas" width="100%" height="100"></canvas>
          </div>

          <!-- 操作按钮 -->
          <div class="d-flex justify-content-end flex-wrap">
            <button
              type="button"
              class="btn btn-outline-danger mr-3"
              id="delete-audio-btn"
            >
              删除音频 <span class="fi-trash"></span>
            </button>
            <button
              type="button"
              class="btn btn-primary"
              id="start-detection-btn"
            >
              <span
                class="spinner-border spinner-border-sm mr-2"
                role="status"
                aria-hidden="true"
                hidden
              ></span>
              <span class="btn-text">开始检测</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 检测结果显示区域 -->
      <div class="mb-3">
        <label class="form-label"><strong>检测结果：</strong></label>
        <div id="result-area" class="border rounded p-3">
          <!-- 初始状态 -->
          <div id="initial-state" class="text-center text-muted">
            <i
              class="fi-magnifying-glass"
              style="font-size: 2.5rem; color: #dee2e6"
            ></i>
            <p class="mt-2 mb-0">请上传音频文件并点击"开始检测"</p>
            <p class="small">检测结果将在此处显示</p>
          </div>

          <!-- 加载状态 -->
          <div id="detection-loading" style="display: none">
            <div class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="sr-only">识别中...</span>
              </div>
              <p class="mt-2">正在分析音频，请稍候...</p>
            </div>
          </div>

          <!-- 检测结果 -->
          <div id="detection-result" style="display: none">
            <div class="row">
              <div class="col-md-4">
                <div class="text-center">
                  <h6 class="text-muted mb-2">AI生成检测</h6>
                  <span
                    class="badge badge-lg"
                    id="ai-result-badge"
                    style="font-size: 1rem; padding: 0.5rem 1rem"
                    >-</span
                  >
                </div>
              </div>
              <div class="col-md-4">
                <div class="text-center">
                  <h6 class="text-muted mb-2">置信度</h6>
                  <div
                    style="font-size: 1.5rem; font-weight: 600; color: #007bff"
                  >
                    <span id="confidence-value">-</span>%
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="text-center">
                  <h6 class="text-muted mb-2">处理时间</h6>
                  <div
                    style="font-size: 1.2rem; font-weight: 500; color: #28a745"
                  >
                    <span id="processing-time">-</span> ms
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div align="center" style="font-size: 16px; color: #000">
  &nbsp;&nbsp;&nbsp;&nbsp; 联系电话: 0592-5998812 &nbsp;&nbsp;&nbsp;&nbsp;
  <a href="https://www.talentedsoft.com/" style="color: #000">公司网站链接</a>
  &nbsp;&nbsp;
</div>
{% endblock content %} {% block scripts %} {{ super() }}
<script
  type="text/javascript"
  src="{{ url_for('static', filename='js/voice_detection.js') }}"
></script>
{% endblock scripts %}
