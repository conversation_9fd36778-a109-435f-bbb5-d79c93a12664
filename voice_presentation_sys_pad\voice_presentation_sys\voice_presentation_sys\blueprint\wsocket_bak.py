# -*- coding: utf-8 -*-
from flask import Blueprint, current_app, jsonify, session
from voice_presentation_sys.extensions import G_WebSocket_Dict
from websocket import create_connection
import traceback


websocket_bp = Blueprint('websocket', __name__)


@websocket_bp.route('/create_websocket', methods=['GET'])
def create_websocket():
    """
    用来创建websocket
    """
    # 防止恶意请求
    socketId = session.get('socketId')
    if socketId:
        try:
            ws = G_WebSocket_Dict[socketId]
            ws.send('end')
            ws.close()  # 关掉websocket
            G_WebSocket_Dict.pop(socketId)  # 删掉响应key的websocket
        except Exception:  # 可能是KeyError等等 不需要做处理
            pass
    # 正式逻辑
    try:
        ws = create_connection("ws://125.77.202.194:58001/dotcwsasr?userid=ts_demo&token=ts_demo&sid=301")
        ws.settimeout(0.01)  # 设置 ws.recv的阻塞超时时间限制 超过了会抛出指定的错误WebSocketTimeoutException(因为流接口不会每次都返回值)
    except Exception:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            content="Create WebSocket Error!"
        )
    # socketId = "".join(random.sample('zyxwvutsrqponmlkjihgfedcba', 5))  # 弄一个长度为5的随机字符串来当socketId
    socketId = 'socket'  # 暂时先固定值用来测试
    G_WebSocket_Dict[socketId] = ws  # 存ws句柄
    session['socketId'] = socketId  # "socket"存session

    return jsonify(
        state="Success",
        content="Create WebSocket Success!"
    )


@websocket_bp.route('/del_websocket', methods=['GET'])
def del_websocket():
    socketId = session.get('socketId')
    if socketId:
        try:
            ws = G_WebSocket_Dict[socketId]
            ws.send('end')
            ws.close()  # 关掉websocket
            G_WebSocket_Dict.pop(socketId)  # 删掉响应key的websocket
        except Exception as e:
            current_app.logger.warn(traceback.format_exc())
            return jsonify(
                state="Error",
                content=""
            )
        return jsonify(
            state="Success",
            content="Delete websocket success!"
        )
    else:
        return jsonify(
            state="Error",
            content="Missing required parameters!"
        )

