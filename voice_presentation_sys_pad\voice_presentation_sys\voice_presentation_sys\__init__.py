# -*- coding: utf-8 -*-
import os, click, logging

from flask import Flask, render_template
# from flask_wtf.csrf import CSRFError
from logging.handlers import RotatingFileHandler

from voice_presentation_sys.extensions import db, bootstrap, migrate
from voice_presentation_sys.settings import config
from voice_presentation_sys.blueprint.voice_presentation import voice_presentation_bp
from voice_presentation_sys.blueprint.ajax import ajax_bp
from voice_presentation_sys.blueprint.wsocket import websocket_bp
from voice_presentation_sys.models import *

basedir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))


def create_app(config_name=None):
    if config_name is None:
        config_name = os.getenv('FLASK_CONFIG', 'development')

    app = Flask('voice_presentation_sys')
    app.config.from_object(config[config_name])

    register_extensions(app)
    register_blueprints(app)
    register_commands(app)
    register_errors(app)
    register_logging(app)
    return app


def register_errors(app):

    @app.errorhandler(400)
    def bad_request(e):
        return render_template('errors/400.html'), 400

    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('errors/500.html'), 500

    # @app.errorhandler(CSRFError)
    # def handle_csrf_error(e):
    #     return render_template('errors/400.html', description=e.description), 400


def register_extensions(app):
    bootstrap.init_app(app)
    db.init_app(app)
    migrate.init_app(app, db)


def register_blueprints(app):
    app.register_blueprint(voice_presentation_bp)
    app.register_blueprint(ajax_bp, url_prefix='/ajax')
    app.register_blueprint(websocket_bp, url_prefix='/ws')


def register_commands(app):
    # 初始化数据库(附带drop参数时数据会被删掉)
    @app.cli.command()
    @click.option('--drop', is_flag=True, help='Create after drop.')
    def initdb(drop):
        """Initialize the database."""
        if drop:
            click.confirm('This operation will delete the database, do you want to continue?', abort=True)
            db.drop_all()
            click.echo('Drop tables.')
        db.create_all()
        click.echo('Initialized database.')
        click.echo('Done')


# 注册邮件日志处理器
def register_logging(app):
    # 日志记录器
    app.logger.setLevel(logging.INFO)  # 设置app.logger的等级为INFO，以记录INFO等级的日志事件

    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')  # 设置日志的输出格式
    # 日志处理器
    file_handler = RotatingFileHandler(os.path.join(basedir, 'logs/voice_presentation_sys.log'),
                                       maxBytes=10 * 1024 * 1024, backupCount=10, encoding="utf-8")  # 创建可重复覆盖的日志处理器(文件到达一定大小后覆盖写入)
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)

    # if not app.debug:
    app.logger.addHandler(file_handler)  # 将处理器注册到app中

    # 工作流程 app.logger记录器捕获异常信息 -> 输出给file_hander处理器 -> file_hander根据给定格式输出到文件中


