{% extends 'base.html' %}
<!-- 前端页面 -->
{% block content %}
    <div class="container p-4">
        <!-- 使用说明 -->
        <div class="alert alert-info">
            <h5>使用说明</h5>
            <ul class="mb-0">
                <li>点击“开始录音”或“上传文件”输入音色克隆目标语音，语音时长推荐在3~10s，复刻效果会更好。</li>
                <li>输入需要转换成语音的文本后，点击“开始合成”生成语音。</li>
                <li>合成完成后可试听并下载合成音频。</li>
            </ul>
        </div>

        <!-- 语音处理模块 -->
        <div class="card">
            <div class="card-body">
                <!-- Prompt 音频上传区域 -->
                <div class="mb-3">
                    <label class="form-label"><strong>在这里上传音色克隆目标的语音：</strong></label>
                    <div class="d-flex align-items-center justify-content-between flex-wrap">
                        <!-- 音频播放器 -->
                        <audio id="audio-player" controls class="flex-grow-1 mr-3" style="min-width: 200px;"></audio>

                        <!-- 操作按钮 -->
                        <div class="mt-2 mt-md-0 d-flex gap-2 flex-wrap justify-content-end">
                            <button id="record-button" class="btn btn-primary" data-type="asr">
                                开始录音 <span class="fi-microphone"></span>
                            </button>
                            <button hidden id="recording-button" class="btn btn-primary">
                                停止录音
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="sr-only"></span>
                                </div>
                            </button>
                            <button id="upload-wav-button" class="btn btn-light" data-toggle="modal" data-target="#confirm-upload">
                                上传文件 <span class="fi-cloud-upload"></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- TTS 输入框 -->
                <div class="mb-3">
                    <label for="tts-content">文本输入用于语音合成：</label>
                    <textarea id="tts-content" class="form-control" rows="5" placeholder="请在这里输入需要转换为语音的内容...">您好，这里是天聪智能声云语音演示中心，很高兴为您服务！ </textarea>
                </div>

                <!-- TTS 合成按钮 -->
                <div class="d-flex justify-content-end mt-4 gap-2 flex-wrap">
                    <button id="tts-generate" class="btn btn-primary" type="button">
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" hidden></span>
                        <span class="btn-text">开始合成</span>
                      </button>                      
                </div>

                <!-- 合成后音频和下载 -->
                <div class="mb-3" id="tts-result" hidden>
                    <label>合成音频预览：</label>
                    <audio id="tts-audio" controls class="w-100"></audio>
                    <div class="d-flex justify-content-end mt-2">
                        <a id="tts-download" class="btn btn-secondary" download="tts_output.wav">下载音频</a>
                    </div>
                </div>
               
            </div>
        </div>

        <!-- 上传音频模态框 -->
        <div class="modal fade" id="confirm-upload" tabindex="-1" role="dialog" aria-labelledby="文件上传" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title">上传音频文件</h6>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="upload-form" enctype="multipart/form-data">
                            <input id="file" type="file" name="audio_file" accept="audio/*" required>
                            <button id="submit-file" type="button" class="btn btn-secondary mt-2" data-dismiss="modal">
                                提交
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div align="center" style="font-size:16px;color:#000"> &nbsp;&nbsp;&nbsp;&nbsp;    联系电话: 0592-5998812 &nbsp;&nbsp;&nbsp;&nbsp;        
     <a href="https://www.talentedsoft.com/" style=" color:#000;">公司网站链接</a> &nbsp;&nbsp;    
    </div>    
{% endblock content %}

{% block scripts %}
    {{ super() }}
    <script type="text/javascript" src="{{ url_for('static', filename='js/voice_clone.js') }}"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
{% endblock scripts %}
   