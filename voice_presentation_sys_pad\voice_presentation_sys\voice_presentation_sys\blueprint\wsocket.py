# -*- coding: utf-8 -*-
from flask import Blueprint, current_app, jsonify, session, request, make_response
from voice_presentation_sys.extensions import G_WebSocket_Dict
from websocket import create_connection
import traceback


websocket_bp = Blueprint('websocket', __name__)


@websocket_bp.route('/create_websocket', methods=['GET'])
def create_websocket():
    """
    用来创建websocket
    """
    socket_id = request.args.get("socket_id", None)
    dest_ws = request.args.get("dest_ws", None)
    if not socket_id or not dest_ws:
        return jsonify(
            state="Error",
            content="参数错误"
        )
    try:
        # 防止有残留的句柄没关闭
        ws = G_WebSocket_Dict[socket_id]
        ws.send('end')
        ws.close()  # 关掉websocket
        G_WebSocket_Dict.pop(socket_id)  # 删掉响应key的websocket
    except Exception:  # 可能是KeyError等等 不做处理
        pass
    # 正式逻辑
    try:
        # 166云集群的内网ip
        ws = create_connection(dest_ws)
        ws.settimeout(0.1)  # 设置 ws.recv的阻塞超时时间限制 超过了会抛出指定的错误WebSocketTimeoutException(因为流接口不会每次都返回值)
    except Exception:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            content="创建 WebSocket 失败"
        )

    G_WebSocket_Dict[socket_id] = ws  # 存ws句柄

    return jsonify(
        state="Success",
        content="创建 WebSocket 成功"
    )


@websocket_bp.route('/del_websocket', methods=['GET'])
def del_websocket():
    socket_id = request.args.get("socket_id", None)
    if socket_id:
        try:
            ws = G_WebSocket_Dict[socket_id]
            ws.send('end')
            ws.close()  # 关掉websocket
            G_WebSocket_Dict.pop(socket_id)  # 删掉响应key的websocket
        except Exception as e:
            current_app.logger.warn(traceback.format_exc())
            return jsonify(
                state="Error",
                content="删除 websocket 失败"
            )
        return jsonify(
            state="Success",
            content="删除 websocket 成功"
        )
    else:
        return jsonify(
            state="Error",
            content="参数错误"
        )

