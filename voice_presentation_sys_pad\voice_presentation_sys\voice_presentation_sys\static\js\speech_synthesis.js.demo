$(function () {

    //扩展js的array，以达到能够 移除指定索引的对象 的操作
    Array.prototype.remove = function (dx) {
        if (isNaN(dx) || dx > this.length) {
            return false;
        }
        for (var i = 0, n = 0; i < this.length; i++) {
            if (this[i] != this[dx]) {
                this[n++] = this[i]
            }
        }
        this.length -= 1
    }

    $('#voice-content').on('keyup', function () {
        $('#word-nums').text(1000 - $('#voice-content').val().length);
        if ($('#voice-content').val().length > 1000) {
            $('#word-nums').text(0);  //长度大于100时0处显示的也只是100
            $('#voice-content').val($('#voice-content').val().substring(0, 1000));  //长度大于100时截取前100个字符
        }
    })

    $('#play-button').on('click', function (e) {
        let content = $('#voice-content');
        let audio_dom = $('#audio')[0];  //audio的dom对象
        let audio_jq = $('#audio');  //audio的jq对象
        let play_button = $('#play-button');
        let load_button = $('#loading-button');
        let download_button = $('#wav-download');

        play_button.attr("disabled", true); //播放按钮不可点击

        if(play_button.attr("data-lanid") == 0 || play_button.attr("data-lanid") == 3)
        {
            $.ajax({
                type: 'POST',
                url: '/ajax/tts',
                data: {
                    'content': content.val(),
                    'spk_id': play_button.attr("data-spkid"),
                    'lan_id': play_button.attr("data-lanid")
                },
                success: function (data) {
                    if (data.state == 'Success') {
                        audio_jq.attr('src', data.msg);  //填充audio的src地址
                        console.log(data.msg)
                        download_button.attr('href', data.msg);  //填充下载按钮的href地址
    
                        audio_dom.addEventListener('play', function () {  //audio播放开始事件监听
                            play_button.removeAttr('disabled')
                            play_button.attr("hidden", true);
                            load_button.attr("hidden", false);
                        });
    
                        audio_dom.addEventListener('ended', function () {  //audio播放结束事件监听
                            play_button.removeAttr('disabled')
                            load_button.attr("hidden", true);
                            play_button.attr("hidden", false); //播放结束启用按钮
                            audio_jq.attr("src", '');
                        }, false);
                    } else {
                        play_button.removeAttr('disabled');
                        toast([data.msg], 1500)
                    }
                },
                error: function (){
                    play_button.removeAttr('disabled');
                    toast(['请重新尝试播放，若多次点击不生效，请联系管理员。'], 1500)
                }
            })
        }
        else if (play_button.attr("data-lanid") == 1) { // 英语，从本地服务器读取 wav 文件播放
            play_button.attr("disabled", true);
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/english.wav';

            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        ``
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 2) { // 越南语
            play_button.attr("disabled", true);
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/vi.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 4) { // 台湾普通话
            play_button.attr("disabled", true);
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/p_50592190_489.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 5) { // 菲律宾语
            play_button.attr("disabled", true);
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/菲律宾语.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 6) { // 马来语
            play_button.attr("disabled", true);
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/马来西亚.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 7) { // 印尼语
            play_button.attr("disabled", true);
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/印尼.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 8) { // 缅语
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/缅甸.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 9) { // 泰语
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/泰语.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 10) { // 柬埔寨语
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/柬埔寨.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
        else if (play_button.attr("data-lanid") == 11) { // 老挝语
        
            // 设定你希望加载的静态 wav 文件路径（相对路径或绝对路径）
            const staticAudioPath = 'https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/audio/老挝.wav';
            
            audio_jq.attr('src', staticAudioPath);              // 设置 audio 的 src
            download_button.attr('href', staticAudioPath);      // 设置下载按钮 href
            audio_dom.load();                                    // 加载音频
            audio_dom.play();                                    // 播放音频
        
            // 播放事件
            audio_dom.addEventListener('play', function () {
                play_button.removeAttr('disabled');
                play_button.attr("hidden", true);
                load_button.attr("hidden", false);
            });
        
            // 播放结束事件
            audio_dom.addEventListener('ended', function () {
                play_button.removeAttr('disabled');
                load_button.attr("hidden", true);
                play_button.attr("hidden", false);
                audio_jq.attr("src", ''); // 清空音频以防止重复播放
            }, { once: true });
        }
    })

//button active listen methods
    $('.lang-choose-tab').on('click', function (event) {
        event.preventDefault()
        let play_btn = $('#play-button');
        play_btn.attr('data-lanid', $(this).attr("data-lanid"));
        play_btn.attr('data-spkid', $(this).attr("data-spkid"));

        let current_tab = $(this);
        $('.lang-choose-tab').each(function () {
            if ($(this) != current_tab) {
                $(this).removeClass("active");
            }
        });
    });

    $('.lang-spks-tab').on('click', function (event) {
        event.preventDefault()
        let play_btn = $('#play-button');
        play_btn.attr('data-lanid', $(this).attr("data-lanid"));
        play_btn.attr('data-spkid', $(this).attr("data-spkid"));

        let current_tab = $(this);
        $('.lang-spks-tab').each(function () {
            if ($(this) != current_tab) {
                $(this).removeClass("active");
            }
        });
    });
})
